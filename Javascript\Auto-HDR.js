// ==UserScript==
// @name         Auto HDR Minimal
// @namespace    http://taeparlaytampermonkey.net/
// @version      3.0
// @description  Minimal HDR effect with optimal performance
// <AUTHOR>
// @match        *://*/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function () {
    'use strict';

    const SCRIPT_NAME = 'AutoHDRSettings';
    const processedElements = new WeakSet();
    let cssFilterString = '';
    let mutationObserver = null;

    // Minimal default settings
    const DEFAULT_SETTINGS = {
        enabled: true,
        brightness: 1.05,
        contrast: 1.15,
        saturation: 1.20,
        excludedSites: [],
        maxCanvasDimension: 1500,
        enableGUI: true
    };

    let settings = { ...DEFAULT_SETTINGS };

    // Settings management
    function loadSettings() {
        const saved = GM_getValue(SCRIPT_NAME, null);
        if (saved) {
            try {
                settings = { ...DEFAULT_SETTINGS, ...JSON.parse(saved) };
            } catch (e) {
                settings = { ...DEFAULT_SETTINGS };
            }
        }
        validateSettings();
    }

    function validateSettings() {
        // Validate numeric fields
        ['brightness', 'contrast', 'saturation'].forEach(field => {
            const val = parseFloat(settings[field]);
            settings[field] = isNaN(val) ? DEFAULT_SETTINGS[field] : val;
        });

        settings.maxCanvasDimension = parseInt(settings.maxCanvasDimension) || DEFAULT_SETTINGS.maxCanvasDimension;
        settings.enabled = Boolean(settings.enabled);
        settings.enableGUI = Boolean(settings.enableGUI);

        if (!Array.isArray(settings.excludedSites)) {
            settings.excludedSites = DEFAULT_SETTINGS.excludedSites;
        }
    }

    function saveSettings() {
        GM_setValue(SCRIPT_NAME, JSON.stringify(settings));
    }

    // Helper functions
    function isCrossOrigin(img) {
        try {
            if (img.src.startsWith('data:')) return false;
            const srcUrl = new URL(img.src, window.location.href);
            return srcUrl.origin !== window.location.origin;
        } catch (e) {
            return true;
        }
    }

    function isSiteExcluded() {
        return settings.excludedSites.some(site =>
            site && window.location.href.includes(site.trim())
        );
    }

    // HDR processing functions
    function applyHDREffectToImage(img) {
        if (img.dataset.hdrApplied || processedElements.has(img)) return;

        if (!img.complete || img.naturalWidth === 0 || img.naturalHeight === 0) return;

        // Use CSS filter for large images or cross-origin
        if (img.naturalWidth > settings.maxCanvasDimension ||
            img.naturalHeight > settings.maxCanvasDimension ||
            isCrossOrigin(img)) {

            if (!cssFilterString) {
                cssFilterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;
            }
            img.style.filter = cssFilterString;
            img.dataset.hdrApplied = 'css-filter';
            processedElements.add(img);
            return;
        }

        // Canvas processing for full control
        processImageWithCanvas(img);
    }

    function processImageWithCanvas(img) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;

        try {
            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // Simple HDR processing
            for (let i = 0; i < data.length; i += 4) {
                let r = data[i], g = data[i + 1], b = data[i + 2];

                // Apply brightness and contrast
                r = ((r - 128) * settings.contrast + 128) * settings.brightness;
                g = ((g - 128) * settings.contrast + 128) * settings.brightness;
                b = ((b - 128) * settings.contrast + 128) * settings.brightness;

                // Apply saturation
                const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                r = gray + (r - gray) * settings.saturation;
                g = gray + (g - gray) * settings.saturation;
                b = gray + (b - gray) * settings.saturation;

                // Clamp values
                data[i] = Math.max(0, Math.min(255, r));
                data[i + 1] = Math.max(0, Math.min(255, g));
                data[i + 2] = Math.max(0, Math.min(255, b));
            }

            ctx.putImageData(imageData, 0, 0);

            if (!img.dataset.originalSrc && !img.src.startsWith('data:')) {
                img.dataset.originalSrc = img.src;
            }
            img.src = canvas.toDataURL();
            img.dataset.hdrApplied = 'canvas-processed';
            processedElements.add(img);

        } catch (e) {
            // Fallback to CSS filter
            if (!cssFilterString) {
                cssFilterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;
            }
            img.style.filter = cssFilterString;
            img.dataset.hdrApplied = 'css-fallback';
            processedElements.add(img);
        }
    }

    function applyHDRToVideos() {
        const videos = document.querySelectorAll('video:not([data-hdrApplied])');
        videos.forEach(video => {
            if (!cssFilterString) {
                cssFilterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;
            }
            video.style.filter = cssFilterString;
            video.dataset.hdrApplied = 'video-processed';
            processedElements.add(video);
        });
    }

    function revertElement(el) {
        if (!el.dataset.hdrApplied) return;

        const wasCanvasProcessed = el.dataset.hdrApplied.includes('canvas-processed');
        const originalSrc = el.dataset.originalSrc;

        el.style.filter = '';

        if (wasCanvasProcessed && originalSrc && el.src?.startsWith('data:image')) {
            el.src = originalSrc;
        }

        ['data-hdrApplied', 'data-originalSrc', 'data-hdrListener'].forEach(attr => {
            el.removeAttribute(attr);
        });

        processedElements.delete(el);
    }

    function processAllMedia() {
        if (!settings.enabled || isSiteExcluded()) {
            document.querySelectorAll('[data-hdrApplied]').forEach(revertElement);
            return;
        }

        // Process images
        const images = document.querySelectorAll('img:not([data-hdrApplied])');
        images.forEach(img => {
            if (img.complete) {
                applyHDREffectToImage(img);
            } else if (!img.dataset.hdrListener) {
                const onLoad = () => {
                    applyHDREffectToImage(img);
                    img.removeEventListener('load', onLoad);
                    img.removeEventListener('error', onError);
                    img.removeAttribute('data-hdrListener');
                };
                const onError = () => {
                    img.dataset.hdrApplied = 'error-load';
                    img.removeEventListener('load', onLoad);
                    img.removeEventListener('error', onError);
                    img.removeAttribute('data-hdrListener');
                };
                img.addEventListener('load', onLoad, { once: true });
                img.addEventListener('error', onError, { once: true });
                img.dataset.hdrListener = 'true';
            }
        });

        applyHDRToVideos();
    }

    const debounce = (fn, delay) => {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => fn(...args), delay);
        };
    };

    const debouncedProcessMedia = debounce(processAllMedia, 100);

    function startObserver() {
        if (mutationObserver) mutationObserver.disconnect();

        if (!settings.enabled || isSiteExcluded()) {
            document.querySelectorAll('[data-hdrApplied]').forEach(revertElement);
            return;
        }

        mutationObserver = new MutationObserver((mutationsList) => {
            let needsProcessing = false;

            for (const mutation of mutationsList) {
                if (mutation.type === 'childList') {
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType === 1) {
                            const tagName = node.tagName;
                            if (tagName === 'IMG' || tagName === 'VIDEO' ||
                                (node.querySelector && node.querySelector('img, video'))) {
                                needsProcessing = true;
                                break;
                            }
                        }
                    }
                }
                if (needsProcessing) break;
            }

            if (needsProcessing) debouncedProcessMedia();
        });

        const target = document.body || document.documentElement;
        if (target) {
            mutationObserver.observe(target, {
                childList: true,
                subtree: true
            });
        }

        debouncedProcessMedia();
    }


    function init() {
        loadSettings();

        if (settings.enableGUI) {
            if (document.body) {
                createSettingsGUI();
            } else {
                document.addEventListener('DOMContentLoaded', createSettingsGUI, { once: true });
            }
        }

        startObserver();

        window.addEventListener('storage', (event) => {
            if (event.key === SCRIPT_NAME) {
                const oldEnabled = settings.enabled;
                loadSettings();
                if (oldEnabled !== settings.enabled) {
                    startObserver();
                }
            }
        });

        if (document.readyState === 'complete') {
            debouncedProcessMedia();
        } else {
            window.addEventListener('load', debouncedProcessMedia, { once: true });
        }
    }

    // Simple GUI
    function createSettingsGUI() {
        if (document.getElementById('autohdr-settings-button') || !document.body) return;

        GM_addStyle(`
            #autohdr-settings-button {
                position: fixed; top: 20px; right: 20px; z-index: 9999;
                background: #3b82f6; color: white; border: none;
                padding: 10px; border-radius: 5px; cursor: pointer;
            }
            #autohdr-settings-panel {
                position: fixed; top: 60px; right: 20px; z-index: 9998;
                background: rgba(0,0,0,0.9); color: white; padding: 20px;
                border-radius: 10px; display: none; width: 300px;
            }
            #autohdr-settings-panel.show { display: block; }
            #autohdr-settings-panel label {
                display: block; margin: 10px 0;
            }
            #autohdr-settings-panel input {
                width: 60px; margin-left: 10px;
            }
            #autohdr-settings-panel button {
                background: #3b82f6; color: white; border: none;
                padding: 10px 20px; border-radius: 5px; cursor: pointer;
                margin-top: 10px;
            }


        const button = document.createElement('button');
        button.id = 'autohdr-settings-button';
        button.textContent = 'HDR';

        const panel = document.createElement('div');
        panel.id = 'autohdr-settings-panel';
        panel.innerHTML = '<h3>HDR Settings</h3>' +
            '<label><input type="checkbox" id="enabled"> Enable HDR</label>' +
            '<label>Brightness: <input type="number" id="brightness" step="0.01" min="0"></label>' +
            '<label>Contrast: <input type="number" id="contrast" step="0.01" min="0"></label>' +
            '<label>Saturation: <input type="number" id="saturation" step="0.01" min="0"></label>' +
            '<label>Excluded Sites: <textarea id="excludedSites" placeholder="site1.com, site2.com"></textarea></label>' +
            '<button id="save">Save</button>';

        function populateSettings() {
            document.getElementById('enabled').checked = settings.enabled;
            document.getElementById('brightness').value = settings.brightness;
            document.getElementById('contrast').value = settings.contrast;
            document.getElementById('saturation').value = settings.saturation;
            document.getElementById('excludedSites').value = settings.excludedSites.join(', ');
        }

        button.addEventListener('click', () => {
            const isVisible = panel.classList.contains('show');
            if (isVisible) {
                panel.classList.remove('show');
            } else {
                panel.classList.add('show');
                populateSettings();
            }
        });

        document.getElementById('save').addEventListener('click', () => {
            settings.enabled = document.getElementById('enabled').checked;
            settings.brightness = parseFloat(document.getElementById('brightness').value) || 1;
            settings.contrast = parseFloat(document.getElementById('contrast').value) || 1;
            settings.saturation = parseFloat(document.getElementById('saturation').value) || 1;
            settings.excludedSites = document.getElementById('excludedSites').value
                .split(',').map(s => s.trim()).filter(s => s);

            cssFilterString = '';
            saveSettings();
            panel.classList.remove('show');
        });

        document.body.appendChild(button);
        document.body.appendChild(panel);
    }


        populateSettings();

        document.body.appendChild(button);
        document.body.appendChild(panel);
    }

    // Initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        init();
    }

})();
